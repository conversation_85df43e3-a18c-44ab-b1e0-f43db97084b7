package com.ariel.app.data.repository

import android.content.Context
import android.net.Uri
import android.util.Log
import com.ariel.app.data.api.RetrofitClient
import com.ariel.app.data.model.CategoriesResponse
import com.ariel.app.data.model.CategoryResponse
import com.ariel.app.data.model.CategoryUpdateRequest
import com.ariel.app.data.model.PaginatedResponse
import com.ariel.app.data.model.Priority
import com.ariel.app.data.model.PriorityUpdateRequest
import com.ariel.app.data.model.RatingRequest
import com.ariel.app.data.model.Status
import com.ariel.app.data.model.StatusUpdateRequest
import com.ariel.app.data.model.Ticket
import com.ariel.app.data.model.UserSignature
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.FileOutputStream

/**
 * Repository class that handles ticket-related operations.
 */
class TicketRepository {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Retrieves the list of tickets for the authenticated user with pagination.
     *
     * @param token The authentication token.
     * @param pageSize The number of tickets per page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getMyTickets(token: String, pageSize: Int = 15): Result<PaginatedResponse<Ticket>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getMyTickets(authToken, pageSize)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch tickets: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves tickets from a specific URL (for pagination).
     *
     * @param token The authentication token.
     * @param url The full URL for the next page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getTicketsFromUrl(token: String, url: String): Result<PaginatedResponse<Ticket>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getTicketsFromUrl(authToken, url)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch tickets: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the list of tickets for the authenticated user filtered by status.
     *
     * @param token The authentication token.
     * @param status The status to filter by.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getMyTicketsByStatus(token: String, status: String): Result<PaginatedResponse<Ticket>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getMyTicketsByStatus(authToken, status)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch tickets by status: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the list of tickets for the authenticated user filtered by group ID.
     *
     * @param token The authentication token.
     * @param groupId The group ID to filter by.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getMyTicketsByGroupId(token: String, groupId: Int): Result<PaginatedResponse<Ticket>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getMyTicketsByGroupId(authToken, groupId)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch tickets by group: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the details of a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @return A Result containing either the ticket details or an Exception.
     */
    suspend fun getTicketDetails(token: String, shortUuid: String): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getTicketDetails(authToken, shortUuid)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch ticket details: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Replies to an existing ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the parent ticket.
     * @param message The message content of the reply.
     * @return A Result containing either the created reply ticket or an Exception.
     */
    suspend fun replyToTicket(token: String, shortUuid: String, message: String): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val messageMap = mapOf("message" to message)
                val response = apiService.replyToTicket(authToken, shortUuid, messageMap)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to reply to ticket: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Replies to an existing ticket with a file attachment.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the parent ticket.
     * @param message The message content of the reply.
     * @param fileUri The URI of the file to attach.
     * @param context The context to access the content resolver.
     * @return A Result containing either the created reply ticket or an Exception.
     */
    suspend fun replyToTicketWithFile(
        token: String,
        shortUuid: String,
        message: String,
        fileUri: Uri,
        context: Context
    ): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"

                // Create a temporary file from the URI
                val inputStream = context.contentResolver.openInputStream(fileUri)
                val fileName = getFileNameFromUri(fileUri, context)
                val tempFile = File(context.cacheDir, fileName)

                FileOutputStream(tempFile).use { outputStream ->
                    inputStream?.copyTo(outputStream)
                }

                // Create request parts
                val messageRequestBody = message.toRequestBody("text/plain".toMediaTypeOrNull())

                // Determine file MIME type
                val mimeType = context.contentResolver.getType(fileUri) ?: "application/octet-stream"
                val fileRequestBody = tempFile.asRequestBody(mimeType.toMediaTypeOrNull())
                val filePart = MultipartBody.Part.createFormData("file", fileName, fileRequestBody)

                // Make API call
                val response = apiService.replyToTicketWithFile(
                    authToken,
                    shortUuid,
                    messageRequestBody,
                    filePart
                )

                // Clean up temporary file
                tempFile.delete()

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to reply to ticket with file: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Gets the file name from a URI.
     *
     * @param uri The URI to get the file name from.
     * @param context The context to access the content resolver.
     * @return The file name.
     */
    private fun getFileNameFromUri(uri: Uri, context: Context): String {
        var fileName = "unknown_file"

        context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val displayNameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                if (displayNameIndex != -1) {
                    fileName = cursor.getString(displayNameIndex)
                }
            }
        }

        return fileName
    }

    /**
     * Retrieves the signature of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user whose signature to retrieve.
     * @return A Result containing either the user's signature or an Exception.
     */
    suspend fun getUserSignature(token: String, shortUuid: String): Result<UserSignature> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getUserSignature(authToken, shortUuid)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch user signature: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the list of available categories.
     *
     * @param token The authentication token.
     * @return A Result containing either the list of categories or an Exception.
     */
    suspend fun getCategories(token: String): Result<List<CategoryResponse>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getCategories(authToken)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!.results)
                } else {
                    Result.failure(Exception("Failed to fetch categories: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Updates the category of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param categoryId The ID of the new category.
     * @return A Result containing either the updated ticket or an Exception.
     */
    suspend fun setTicketCategory(token: String, shortUuid: String, categoryId: Int): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val categoryRequest = CategoryUpdateRequest(categoryId)
                Log.d("TicketRepository", "Sending category update request: $categoryRequest for ticket: $shortUuid")

                val response = apiService.setTicketCategory(authToken, shortUuid, categoryRequest)

                if (response.isSuccessful && response.body() != null) {
                    Log.d("TicketRepository", "Category update successful")
                    Result.success(response.body()!!)
                } else {
                    Log.e("TicketRepository", "Category update failed: ${response.code()} - ${response.message()}")
                    Result.failure(Exception("Failed to update ticket category: ${response.message()} (${response.code()})"))
                }
            } catch (e: Exception) {
                Log.e("TicketRepository", "Exception during category update", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the list of available priorities.
     *
     * @param token The authentication token.
     * @return A Result containing either the list of priorities or an Exception.
     */
    suspend fun getPriorities(token: String): Result<List<Priority>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getPriorities(authToken)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch priorities: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Updates the priority of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param priorityValue The value of the new priority.
     * @return A Result containing either the updated ticket or an Exception.
     */
    suspend fun setTicketPriority(token: String, shortUuid: String, priorityValue: String): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val priorityRequest = PriorityUpdateRequest(priorityValue)
                Log.d("TicketRepository", "Sending priority update request: $priorityRequest for ticket: $shortUuid")

                val response = apiService.setTicketPriority(authToken, shortUuid, priorityRequest)

                if (response.isSuccessful && response.body() != null) {
                    Log.d("TicketRepository", "Priority update successful")
                    Result.success(response.body()!!)
                } else {
                    Log.e("TicketRepository", "Priority update failed: ${response.code()} - ${response.message()}")
                    Result.failure(Exception("Failed to update ticket priority: ${response.message()} (${response.code()})"))
                }
            } catch (e: Exception) {
                Log.e("TicketRepository", "Exception during priority update", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the list of available statuses.
     *
     * @param token The authentication token.
     * @return A Result containing either the list of statuses or an Exception.
     */
    suspend fun getStatuses(token: String): Result<List<Status>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getStatuses(authToken)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch statuses: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Updates the status of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param statusValue The value of the new status.
     * @return A Result containing either the updated ticket or an Exception.
     */
    suspend fun setTicketStatus(token: String, shortUuid: String, statusValue: String): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val statusRequest = StatusUpdateRequest(statusValue)
                Log.d("TicketRepository", "Sending status update request: $statusRequest for ticket: $shortUuid")

                val response = apiService.setTicketStatus(authToken, shortUuid, statusRequest)

                if (response.isSuccessful && response.body() != null) {
                    Log.d("TicketRepository", "Status update successful")
                    Result.success(response.body()!!)
                } else {
                    Log.e("TicketRepository", "Status update failed: ${response.code()} - ${response.message()}")
                    Result.failure(Exception("Failed to update ticket status: ${response.message()} (${response.code()})"))
                }
            } catch (e: Exception) {
                Log.e("TicketRepository", "Exception during status update", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Deletes a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket to delete.
     * @return A Result indicating success or failure.
     */
    suspend fun deleteTicket(token: String, shortUuid: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                Log.d("TicketRepository", "Sending delete request for ticket: $shortUuid")

                val response = apiService.deleteTicket(authToken, shortUuid)

                if (response.isSuccessful) {
                    Log.d("TicketRepository", "Ticket deletion successful")
                    Result.success(Unit)
                } else {
                    Log.e("TicketRepository", "Ticket deletion failed: ${response.code()} - ${response.message()}")
                    Result.failure(Exception("Failed to delete ticket: ${response.message()} (${response.code()})"))
                }
            } catch (e: Exception) {
                Log.e("TicketRepository", "Exception during ticket deletion", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the groups of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Result containing either the list of groups or an Exception.
     */
    suspend fun getUserGroups(token: String, shortUuid: String): Result<List<String>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                Log.d("TicketRepository", "Fetching groups for user: $shortUuid")

                val response = apiService.getUserGroups(authToken, shortUuid)

                if (response.isSuccessful && response.body() != null) {
                    Log.d("TicketRepository", "User groups fetch successful: ${response.body()}")
                    Result.success(response.body()!!)
                } else {
                    Log.e("TicketRepository", "User groups fetch failed: ${response.code()} - ${response.message()}")
                    Result.failure(Exception("Failed to fetch user groups: ${response.message()} (${response.code()})"))
                }
            } catch (e: Exception) {
                Log.e("TicketRepository", "Exception during user groups fetch", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the full name of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Result containing either the user's full name or an Exception.
     */
    suspend fun getUserFullName(token: String, shortUuid: String): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                Log.d("TicketRepository", "Fetching full name for user: $shortUuid")

                val response = apiService.getUserFullName(authToken, shortUuid)

                if (response.isSuccessful && response.body() != null) {
                    Log.d("TicketRepository", "User full name fetch successful: ${response.body()}")
                    Result.success(response.body()!!)
                } else {
                    Log.e("TicketRepository", "User full name fetch failed: ${response.code()} - ${response.message()}")
                    Result.failure(Exception("Failed to fetch user full name: ${response.message()} (${response.code()})"))
                }
            } catch (e: Exception) {
                Log.e("TicketRepository", "Exception during user full name fetch", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Rates a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param rating The rating value (1-5).
     * @return A Result containing either the updated ticket or an Exception.
     */
    suspend fun rateTicket(token: String, shortUuid: String, rating: Int): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val ratingRequest = RatingRequest(rating)
                Log.d("TicketRepository", "Sending rating request: $ratingRequest for ticket: $shortUuid")

                val response = apiService.rateTicket(authToken, shortUuid, ratingRequest)

                if (response.isSuccessful && response.body() != null) {
                    Log.d("TicketRepository", "Rating update successful")
                    Result.success(response.body()!!)
                } else {
                    Log.e("TicketRepository", "Rating update failed: ${response.code()} - ${response.message()}")
                    Result.failure(Exception("Failed to rate ticket: ${response.message()} (${response.code()})"))
                }
            } catch (e: Exception) {
                Log.e("TicketRepository", "Exception during rating update", e)
                Result.failure(e)
            }
        }
    }
}
